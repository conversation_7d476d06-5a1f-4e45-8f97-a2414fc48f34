const fs = require('fs');
const path = require('path');

console.log('🔍 验证Voice Service项目结构...\n');

// 必需的文件和目录
const requiredStructure = {
  files: [
    // 根配置文件
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    '.env.example',
    '.gitignore',
    '.eslintrc.js',
    '.prettierrc',
    'Dockerfile',
    'docker-compose.yml',
    'README.md',
    
    // 应用核心文件
    'src/main.ts',
    'src/app.module.ts',
    
    // 语音模块
    'src/voice/voice.module.ts',
    'src/voice/voice.controller.ts',
    'src/voice/voice.service.ts',
    'src/voice/voice.gateway.ts',
    'src/voice/voice.service.spec.ts',
    
    // 语音识别模块
    'src/speech-recognition/speech-recognition.module.ts',
    'src/speech-recognition/speech-recognition.service.ts',
    
    // 语音合成模块
    'src/speech-synthesis/speech-synthesis.module.ts',
    'src/speech-synthesis/speech-synthesis.service.ts',
    
    // 音频处理模块
    'src/audio-processing/audio-processing.module.ts',
    'src/audio-processing/audio-processing.service.ts',
    
    // 嘴形同步模块
    'src/lip-sync/lip-sync.module.ts',
    'src/lip-sync/lip-sync.service.ts',
    
    // 公共组件
    'src/common/guards/jwt-auth.guard.ts',
    'src/common/guards/ws-jwt.guard.ts',
    'src/common/dto/voice.dto.ts',
    'src/common/filters/http-exception.filter.ts',
    'src/common/interceptors/logging.interceptor.ts',
    
    // 健康检查模块
    'src/health/health.module.ts',
    'src/health/health.controller.ts',
    'src/health/health.service.ts',
    
    // 验证脚本
    'scripts/verify-project.js',
  ],
  directories: [
    'src',
    'src/voice',
    'src/speech-recognition',
    'src/speech-synthesis',
    'src/audio-processing',
    'src/lip-sync',
    'src/common',
    'src/common/guards',
    'src/common/dto',
    'src/common/filters',
    'src/common/interceptors',
    'src/health',
    'scripts',
  ]
};

let allValid = true;

// 检查目录结构
console.log('📁 检查目录结构:');
requiredStructure.directories.forEach(dir => {
  const dirPath = path.join(__dirname, '..', dir);
  if (fs.existsSync(dirPath)) {
    console.log(`✅ ${dir}/`);
  } else {
    console.log(`❌ ${dir}/ - 目录缺失`);
    allValid = false;
  }
});

console.log('\n📄 检查必需文件:');
requiredStructure.files.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件缺失`);
    allValid = false;
  }
});

// 检查package.json依赖
console.log('\n📦 检查关键依赖:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  const requiredDependencies = [
    '@nestjs/common',
    '@nestjs/core',
    '@nestjs/platform-express',
    '@nestjs/microservices',
    '@nestjs/config',
    '@nestjs/swagger',
    '@nestjs/event-emitter',
    '@nestjs/bull',
    '@nestjs/platform-socket.io',
    '@nestjs/jwt',
    '@nestjs/passport',
    '@nestjs/terminus',
    'class-validator',
    'class-transformer',
    'socket.io',
    'redis',
    'fluent-ffmpeg',
    'microsoft-cognitiveservices-speech-sdk',
    'openai',
  ];
  
  let depsValid = true;
  requiredDependencies.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 依赖缺失`);
      depsValid = false;
      allValid = false;
    }
  });
  
  if (depsValid) {
    console.log('✅ 所有必需依赖都已配置');
  }
} catch (error) {
  console.log('❌ 无法读取package.json');
  allValid = false;
}

// 检查TypeScript配置
console.log('\n⚙️  检查TypeScript配置:');
try {
  const tsConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'tsconfig.json'), 'utf8'));
  if (tsConfig.compilerOptions) {
    console.log('✅ TypeScript配置正常');
  } else {
    console.log('❌ TypeScript配置异常');
    allValid = false;
  }
} catch (error) {
  console.log('❌ 无法读取tsconfig.json');
  allValid = false;
}

// 检查NestJS配置
console.log('\n🏗️  检查NestJS配置:');
try {
  const nestConfig = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'nest-cli.json'), 'utf8'));
  if (nestConfig.sourceRoot === 'src') {
    console.log('✅ NestJS配置正常');
  } else {
    console.log('❌ NestJS配置异常');
    allValid = false;
  }
} catch (error) {
  console.log('❌ 无法读取nest-cli.json');
  allValid = false;
}

// 最终结果
console.log('\n' + '='.repeat(50));
if (allValid) {
  console.log('🎉 Voice Service项目结构验证通过！');
  console.log('\n📝 下一步操作:');
  console.log('1. 配置环境变量: cp .env.example .env');
  console.log('2. 编辑 .env 文件配置语音服务API密钥');
  console.log('3. 安装依赖: npm install');
  console.log('4. 启动Redis: docker-compose up -d redis');
  console.log('5. 启动服务: npm run start:dev');
  console.log('6. 访问API文档: http://localhost:4010/api/docs');
  process.exit(0);
} else {
  console.log('❌ Voice Service项目结构验证失败！');
  console.log('请检查上述缺失的文件和目录。');
  process.exit(1);
}
